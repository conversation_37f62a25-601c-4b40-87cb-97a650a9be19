#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def simplify_text(text):
    """简化文字，只保留安全字符"""
    # 扩展的中文词汇映射
    replacements = {
        '请输入要添加的文字': 'Sample Text',
        '不是有毒吧': 'Toxic',
        '有毒': 'Toxic', 
        '好的': 'OK',
        '谢谢': 'Thanks',
        '你好': 'Hello',
        '再见': 'Bye',
        '是的': 'Yes',
        '不是': 'No',
        '可以': 'OK',
        '没问题': 'No Problem',
        '太好了': 'Great',
        '厉害': 'Amazing',
        '加油': 'Go',
        '成功': 'Success',
        '失败': 'Fail',
        '开心': 'Happy',
        '难过': 'Sad',
        '生气': 'Angry',
        '喜欢': 'Like',
        '爱': 'Love',
        '美丽': 'Beautiful',
        '可爱': 'Cute',
        '聪明': 'Smart',
        '快': 'Fast',
        '慢': 'Slow',
        '大': 'Big',
        '小': 'Small'
    }
    
    # 首先进行完整匹配替换
    if text in replacements:
        return replacements[text]
    
    # 然后进行部分匹配替换
    for chinese, english in replacements.items():
        if chinese in text:
            text = text.replace(chinese, english)
    
    # 移除所有非ASCII字符，只保留安全字符
    safe_chars = []
    for char in text:
        # 只保留ASCII字母、数字、空格和基本标点
        if ord(char) < 128 and (char.isalnum() or char in ' .,!?-()'):
            safe_chars.append(char)
    
    result = ''.join(safe_chars).strip()
    
    # 如果结果为空或太短，使用默认文字
    if not result or len(result) < 1:
        result = 'Text'
    
    return result

def test_ffmpeg_command(text, safe_text):
    """测试FFmpeg命令格式"""
    # 模拟FFmpeg命令构建
    actual_font_size = 24
    text_x = 50
    text_y = 60
    
    text_filter = f"drawtext=text='{safe_text}':fontcolor=black:fontsize={actual_font_size}:x={text_x}:y={text_y}"
    
    print(f"原始文字: {text}")
    print(f"转换后: {safe_text}")
    print(f"FFmpeg滤镜: {text_filter}")
    print("-" * 50)

if __name__ == "__main__":
    print("文字转换和FFmpeg命令测试")
    print("=" * 50)
    
    test_cases = [
        '请输入要添加的文字',
        '不是有毒吧',
        'Hello World',
        '你好世界123',
        '特殊符号@#$%^&*()',
        '混合文字Hello你好123',
        '',
        '   ',
        '纯中文测试',
        'Pure English Test'
    ]
    
    for text in test_cases:
        safe_text = simplify_text(text)
        test_ffmpeg_command(text, safe_text)
    
    print("测试完成！")
