#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证应用程序基本功能
"""

import sys
import os
from pathlib import Path

def test_imports():
    """测试导入依赖"""
    print("测试导入依赖...")
    try:
        import PySide6
        print(f"✓ PySide6 版本: {PySide6.__version__}")
    except ImportError as e:
        print(f"✗ PySide6 导入失败: {e}")
        return False
        
    try:
        import cv2
        print(f"✓ OpenCV 版本: {cv2.__version__}")
    except ImportError as e:
        print(f"✗ OpenCV 导入失败: {e}")
        return False
        
    try:
        import numpy as np
        print(f"✓ NumPy 版本: {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy 导入失败: {e}")
        return False
        
    return True

def test_ffmpeg_path():
    """测试FFmpeg路径"""
    print("\n测试FFmpeg路径...")
    ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
    
    if ffmpeg_path.exists():
        print(f"✓ FFmpeg 路径存在: {ffmpeg_path}")
        return True
    else:
        print(f"✗ FFmpeg 路径不存在: {ffmpeg_path}")
        print("请检查FFmpeg安装路径或修改main.py中的路径设置")
        return False

def test_output_directory():
    """测试输出目录"""
    print("\n测试输出目录...")
    output_dir = Path("E:/000混剪文件夹/后贴")
    
    try:
        output_dir.mkdir(parents=True, exist_ok=True)
        if output_dir.exists():
            print(f"✓ 输出目录可用: {output_dir}")
            return True
    except Exception as e:
        print(f"✗ 输出目录创建失败: {e}")
        return False

def test_ui_file():
    """测试UI文件"""
    print("\n测试UI文件...")
    ui_file = Path("ui_main_window.py")
    
    if ui_file.exists():
        print(f"✓ UI文件存在: {ui_file}")
        try:
            from ui_main_window import Ui_MainWindow
            print("✓ UI文件导入成功")
            return True
        except ImportError as e:
            print(f"✗ UI文件导入失败: {e}")
            return False
    else:
        print(f"✗ UI文件不存在: {ui_file}")
        return False

def main():
    """主测试函数"""
    print("=== 视频封边处理工具 - 环境测试 ===\n")
    
    tests = [
        test_imports,
        test_ffmpeg_path,
        test_output_directory,
        test_ui_file
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✓ 所有测试通过！可以启动应用程序。")
        return True
    else:
        print("✗ 部分测试失败，请检查环境配置。")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
