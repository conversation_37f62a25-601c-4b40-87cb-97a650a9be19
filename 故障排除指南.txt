视频封边处理工具 - 故障排除指南
================================

🚨 常见错误及解决方案
-------------------

1. 【FFmpeg Fontconfig错误】
错误信息: "Fontconfig error: Cannot load default config file"
解决方案:
- 这是FFmpeg字体配置问题，不影响基本功能
- 程序已优化，会自动处理此错误
- 如果仍有问题，可以暂时禁用文字功能

2. 【文字编码错误】
错误信息: "No option name near" 或 "Error parsing filterchain"
解决方案:
- 程序已内置中文转换功能
- 避免使用特殊符号和复杂中文句子
- 推荐使用简单的中英文词汇

3. 【线程错误】
错误信息: "Cannot set parent, new parent is in a different thread"
解决方案:
- 程序已修复此问题
- 如果仍出现，请重启程序

🔧 故障排除步骤
--------------

步骤1: 清理缓存
- 运行 "清理缓存.bat"
- 或手动删除 __pycache__ 文件夹

步骤2: 测试FFmpeg
- 运行 "测试FFmpeg.bat"
- 确认FFmpeg路径正确

步骤3: 分步测试
1. 只选择视频，不添加其他元素
2. 添加封边图片
3. 添加logo
4. 最后添加文字

步骤4: 检查文件路径
- 确保视频文件路径不包含特殊字符
- 确保封边图片是PNG格式
- 确保logo图片格式正确

🛠️ 高级故障排除
--------------

如果基本功能都无法工作:

1. 检查FFmpeg安装
   - 路径: D:\FFmpeg\ffmpeg-2025-03-31-git-35c091f4b7-full_build\bin\ffmpeg.exe
   - 确认文件存在且可执行

2. 检查Python环境
   - Python 3.12
   - PySide6 >= 6.5.0
   - OpenCV >= 4.8.0

3. 检查文件权限
   - 输出目录: E:\000混剪文件夹\后贴
   - 确保有写入权限

4. 查看详细错误
   - 程序会在控制台显示详细错误信息
   - 记录错误信息以便调试

📝 临时解决方案
--------------

如果文字功能有问题:
1. 暂时不勾选"启用文字"
2. 只使用视频+封边+logo功能
3. 后续版本会进一步优化文字功能

如果logo不显示:
1. 确保logo图片不是太大
2. 尝试使用PNG格式的logo
3. 检查logo文件是否损坏

如果封边不显示:
1. 确保封边图片是PNG格式
2. 确保图片包含透明通道
3. 检查图片尺寸是否合适

🔄 重置程序
----------

完全重置程序状态:
1. 关闭程序
2. 运行 "清理缓存.bat"
3. 删除临时文件
4. 重新启动程序

📞 获取帮助
----------

如果问题仍然存在:
1. 记录完整的错误信息
2. 记录操作步骤
3. 检查是否有新版本更新
4. 联系技术支持

💡 性能优化建议
--------------

1. 使用较小的视频文件进行测试
2. 确保有足够的磁盘空间
3. 关闭不必要的其他程序
4. 使用SSD硬盘可提高处理速度
