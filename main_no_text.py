# -*- coding: utf-8 -*-
# 测试版本 - 禁用文字功能
import sys
import subprocess
import threading
from pathlib import Path

from PySide6.QtWidgets import (QApplication, QMainWindow, QFileDialog, 
                               QGraphicsScene, QGraphicsView, QGraphicsPixmapItem,
                               QGraphicsTextItem, QMessageBox, QSlider, QVBoxLayout, 
                               QHBoxLayout, QWidget, QLabel)
from PySide6.QtCore import Qt, QRectF, QPointF, QThread, Signal
from PySide6.QtGui import QPixmap, QImage, QFont, QColor, QPainter, QTransform, QPen
import cv2

from ui_main_window import Ui_MainWindow


class VideoProcessingThread(QThread):
    """视频处理线程"""
    progress_updated = Signal(str)
    processing_finished = Signal(bool, str)
    
    def __init__(self, cmd):
        super().__init__()
        self.cmd = cmd
        
    def run(self):
        """在后台线程中执行FFmpeg命令"""
        try:
            self.progress_updated.emit("开始处理视频...")
            result = subprocess.run(self.cmd, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                self.processing_finished.emit(True, "视频处理完成！")
            else:
                error_msg = result.stderr if result.stderr else result.stdout
                self.processing_finished.emit(False, f"视频处理失败！\n错误信息：{error_msg}")
                print("FFmpeg错误:", error_msg)
                
        except Exception as e:
            self.processing_finished.emit(False, f"处理过程中发生错误：{str(e)}")
            print("异常:", str(e))


class VideoProcessor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        
        self.setWindowTitle("视频封边处理工具 - 测试版（无文字功能）")
        
        # 初始化变量
        self.video_path = None
        self.border_path = None
        self.logo_path = None
        self.output_dir = Path("E:/000混剪文件夹/后贴")
        self.ffmpeg_path = Path("D:/FFmpeg/ffmpeg-2025-03-31-git-35c091f4b7-full_build/bin/ffmpeg.exe")
        
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化图形场景
        self.scene = QGraphicsScene()
        self.ui.graphicsView.setScene(self.scene)
        self.ui.graphicsView.setRenderHint(QPainter.Antialiasing)
        
        # 图层项目
        self.video_item = None
        self.border_item = None
        self.logo_item = None
        
        # 视频相关
        self.video_cap = None
        self.total_frames = 0
        self.current_frame = 0
        
        # 视频处理线程
        self.processing_thread = None
        
        # 连接信号槽
        self.setup_connections()
        
        # 禁用文字功能
        self.ui.checkBox.setEnabled(False)
        self.ui.textEdit.setEnabled(False)
        self.ui.checkBox.setText("文字功能已禁用（测试版）")
        
    def setup_connections(self):
        """设置信号槽连接"""
        self.ui.pushButton.clicked.connect(self.select_video)
        self.ui.pushButton_2.clicked.connect(self.select_border)
        self.ui.pushButton_3.clicked.connect(self.select_logo)
        self.ui.pushButton_4.clicked.connect(self.process_video)
        
    def select_video(self):
        """选择视频文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择视频文件", "", 
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv *.flv)"
        )
        if file_path:
            self.video_path = file_path
            self.load_video_preview()
            
    def select_border(self):
        """选择封边图片"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择封边图片", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_path:
            self.border_path = file_path
            self.load_border_image()
            
    def select_logo(self):
        """选择logo水印"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Logo水印", "", 
            "图片文件 (*.png *.jpg *.jpeg *.bmp)"
        )
        if file_path:
            self.logo_path = file_path
            self.load_logo_image()
            
    def load_video_preview(self):
        """加载视频预览"""
        if not self.video_path:
            return
            
        if self.video_cap:
            self.video_cap.release()
            
        self.video_cap = cv2.VideoCapture(self.video_path)
        if not self.video_cap.isOpened():
            QMessageBox.warning(self, "警告", "无法打开视频文件！")
            return
            
        self.total_frames = int(self.video_cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(self.video_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(self.video_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        scene_width = self.ui.graphicsView.width() - 20
        scene_height = self.ui.graphicsView.height() - 20
        
        video_ratio = width / height
        view_ratio = scene_width / scene_height
        
        if video_ratio > view_ratio:
            self.preview_width = scene_width
            self.preview_height = int(scene_width / video_ratio)
        else:
            self.preview_height = scene_height
            self.preview_width = int(scene_height * video_ratio)
            
        self.scene.setSceneRect(0, 0, self.preview_width, self.preview_height)
        self.current_frame = 0
        self.show_frame(0)
        
    def show_frame(self, frame_number):
        """显示指定帧"""
        if not self.video_cap or not self.video_cap.isOpened():
            return
            
        self.video_cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = self.video_cap.read()
        
        if not ret:
            return
            
        try:
            height, width, _ = frame.shape
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            q_image = q_image.scaled(self.preview_width, self.preview_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            pixmap = QPixmap.fromImage(q_image)
            
            if self.video_item is None:
                self.video_item = QGraphicsPixmapItem(pixmap)
                self.video_item.setZValue(0)
                self.scene.addItem(self.video_item)
            else:
                self.video_item.setPixmap(pixmap)
                
            self.current_frame = frame_number
                
        except Exception as e:
            print(f"显示视频帧时出错: {e}")
            
    def load_border_image(self):
        """加载封边图片"""
        if not self.border_path:
            return
            
        pixmap = QPixmap(self.border_path)
        if hasattr(self, 'preview_width') and hasattr(self, 'preview_height'):
            pixmap = pixmap.scaled(self.preview_width, self.preview_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        
        if self.border_item is None:
            self.border_item = QGraphicsPixmapItem(pixmap)
            self.border_item.setZValue(2)
            self.scene.addItem(self.border_item)
        else:
            self.border_item.setPixmap(pixmap)
            
    def load_logo_image(self):
        """加载logo图片"""
        if not self.logo_path:
            return
            
        pixmap = QPixmap(self.logo_path)
        max_size = 100
        if hasattr(self, 'preview_width'):
            max_size = min(self.preview_width // 5, 150)
            
        if pixmap.width() > max_size or pixmap.height() > max_size:
            pixmap = pixmap.scaled(max_size, max_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        
        if self.logo_item is None:
            self.logo_item = QGraphicsPixmapItem(pixmap)
            self.logo_item.setZValue(4)
            default_x = getattr(self, 'preview_width', 400) - pixmap.width() - 20
            default_y = 20
            self.logo_item.setPos(default_x, default_y)
            self.scene.addItem(self.logo_item)
        else:
            self.logo_item.setPixmap(pixmap)
            
    def process_video(self):
        """处理视频"""
        if not self.video_path:
            QMessageBox.warning(self, "警告", "请先选择视频文件！")
            return
            
        if not self.border_path:
            QMessageBox.warning(self, "警告", "请先选择封边图片！")
            return
            
        if not self.ffmpeg_path.exists():
            QMessageBox.critical(self, "错误", f"FFmpeg未找到！\n请检查路径：{self.ffmpeg_path}")
            return
            
        self.ui.pushButton_4.setEnabled(False)
        self.ui.pushButton_4.setText("处理中...")
        
        video_name = Path(self.video_path).stem
        output_path = self.output_dir / f"{video_name}_processed.mp4"
        
        cmd = self._build_ffmpeg_command(output_path)
        print("FFmpeg命令:", " ".join(cmd))
        
        self.processing_thread = VideoProcessingThread(cmd)
        self.processing_thread.progress_updated.connect(self.on_progress_updated)
        self.processing_thread.processing_finished.connect(self.on_processing_finished)
        self.processing_thread.start()
        
    def on_progress_updated(self, message):
        """处理进度更新"""
        print(f"处理进度: {message}")
        
    def on_processing_finished(self, success, message):
        """处理完成回调"""
        self.ui.pushButton_4.setEnabled(True)
        self.ui.pushButton_4.setText("融合")
        
        if success:
            video_name = Path(self.video_path).stem
            output_path = self.output_dir / f"{video_name}_processed.mp4"
            QMessageBox.information(self, "成功", f"视频处理完成！\n输出路径：{output_path}")
        else:
            QMessageBox.critical(self, "错误", message)
            
    def _build_ffmpeg_command(self, output_path):
        """构建FFmpeg命令（无文字版本）"""
        cmd = [str(self.ffmpeg_path), "-i", self.video_path]
        cmd.extend(["-i", self.border_path])
        
        filter_complex = "[0:v]scale=1080:1920:force_original_aspect_ratio=decrease,pad=1080:1920:(ow-iw)/2:(oh-ih)/2[video];[1:v]scale=1080:1920[border];[video][border]overlay=0:0:format=auto,format=yuv420p[tmp]"
        
        input_count = 2
        current_output = "[tmp]"
        
        if self.logo_path and self.logo_item:
            cmd.extend(["-i", self.logo_path])
            logo_x = int(self.logo_item.pos().x())
            logo_y = int(self.logo_item.pos().y())
            filter_complex += f";[{input_count}:v]scale=iw:ih[logo];{current_output}[logo]overlay={logo_x}:{logo_y}[final]"
            current_output = "[final]"
            
        cmd.extend(["-filter_complex", filter_complex])
        cmd.extend(["-map", current_output.strip("[]")])
        cmd.extend(["-map", "0:a?"])
        cmd.extend(["-c:v", "libx264", "-preset", "medium", "-crf", "23"])
        cmd.extend(["-c:a", "aac", "-b:a", "128k"])
        cmd.extend(["-y", str(output_path)])
        
        return cmd
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.video_cap:
            self.video_cap.release()
            
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.quit()
            self.processing_thread.wait(3000)
            
        event.accept()


def main():
    app = QApplication(sys.argv)
    window = VideoProcessor()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
