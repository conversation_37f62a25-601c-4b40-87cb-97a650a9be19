@echo off
echo 测试FFmpeg基本功能...
echo.

set FFMPEG_PATH=D:\FFmpeg\ffmpeg-2025-03-31-git-35c091f4b7-full_build\bin\ffmpeg.exe

echo 1. 检查FFmpeg是否存在...
if exist "%FFMPEG_PATH%" (
    echo ✓ FFmpeg文件存在
) else (
    echo ✗ FFmpeg文件不存在: %FFMPEG_PATH%
    pause
    exit /b 1
)

echo.
echo 2. 测试FFmpeg版本信息...
"%FFMPEG_PATH%" -version

echo.
echo 3. 测试简单的视频处理（无文字）...
echo 请确保有测试视频文件...

REM 创建一个简单的测试命令
echo.
echo 测试命令示例:
echo "%FFMPEG_PATH%" -i input.mp4 -i border.png -filter_complex "[0:v]scale=1080:1920[video];[1:v]scale=1080:1920[border];[video][border]overlay=0:0[output]" -map "[output]" -map 0:a? -c:v libx264 -c:a aac -t 5 test_output.mp4

echo.
echo 如果上述命令可以正常工作，说明FFmpeg基本功能正常。
echo 问题可能出现在文字处理部分。

pause
