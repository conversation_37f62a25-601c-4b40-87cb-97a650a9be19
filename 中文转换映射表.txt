中文文字转换映射表
==================

为了解决FFmpeg对中文字符支持的问题，程序内置了智能中英文转换功能。
以下是支持的中文词汇及其对应的英文转换：

🗣️ 常用语句
-----------
不是有毒吧 -> Toxic?
有毒 -> Toxic
好的 -> OK
谢谢 -> Thanks
你好 -> Hello
再见 -> Bye
是的 -> Yes
不是 -> No
可以 -> OK
没问题 -> No Problem

😊 情感表达
-----------
开心 -> Happy
难过 -> Sad
生气 -> Angry
惊讶 -> Surprised
喜欢 -> Like
讨厌 -> Hate
爱 -> Love

👍 评价词汇
-----------
太好了 -> Great
厉害 -> Amazing
加油 -> Go
努力 -> Work Hard
成功 -> Success
失败 -> Fail
美丽 -> Beautiful
帅气 -> Handsome
可爱 -> Cute
聪明 -> Smart
笨 -> Stupid

📏 描述词汇
-----------
快 -> Fast
慢 -> Slow
大 -> Big
小 -> Small
高 -> Tall
矮 -> Short
胖 -> Fat
瘦 -> Thin

🔧 转换规则
-----------
1. 优先匹配完整词汇
2. 部分匹配替换
3. 移除无法转换的中文字符
4. 保留ASCII字符和空格
5. 清理特殊符号
6. 如果结果为空，显示"Text"

💡 使用建议
-----------
- 尽量使用映射表中的中文词汇，转换效果最佳
- 可以组合使用，如"你好，很开心" -> "Hello，Happy"
- 避免使用复杂的中文句子
- 英文和数字可以直接使用

⚠️ 注意事项
-----------
- 转换是自动进行的，无需手动操作
- 程序会在控制台显示转换结果
- 如果发现常用词汇未包含，可以联系开发者添加
- 转换后的文字会在最终视频中显示
